export default {
	// Use Node.js environment for testing
	testEnvironment: 'node',

	// Automatically clear mock calls between every test
	clearMocks: true,

	// Indicates whether the coverage information should be collected while executing the test
	collectCoverage: false,

	// The directory where <PERSON><PERSON> should output its coverage files
	coverageDirectory: 'coverage',

	// A list of paths to directories that <PERSON><PERSON> should use to search for files in
	roots: ['<rootDir>/tests'],

	// The glob patterns Je<PERSON> uses to detect test files
	testMatch: ['**/__tests__/**/*.js', '**/?(*.)+(spec|test).js'],

	// Enable ES modules support
	preset: null,

	// Transform files - empty for ES modules
	transform: {},

	// Disable transformations for node_modules except specific packages
	transformIgnorePatterns: [
		'/node_modules/(?!(chalk|boxen|gradient-string|figlet|ora|inquirer)/)'
	],

	// Set moduleNameMapper for absolute paths and ES module compatibility
	moduleNameMapper: {
		'^@/(.*)$': '<rootDir>/$1',
		'^(\\.{1,2}/.*)\\.js$': '$1'
	},

	// Setup module aliases
	moduleDirectories: ['node_modules', '<rootDir>'],

	// Configure test coverage thresholds (lowered for current phase)
	coverageThreshold: {
		global: {
			branches: 20,
			functions: 25,
			lines: 25,
			statements: 25
		}
	},

	// Generate coverage report in these formats
	coverageReporters: ['text', 'lcov', 'html'],

	// Verbose output
	verbose: true,

	// Setup file
	setupFilesAfterEnv: ['<rootDir>/tests/setup.js'],

	// ES modules globals
	globals: {
		'ts-jest': {
			useESM: true
		}
	}
};
