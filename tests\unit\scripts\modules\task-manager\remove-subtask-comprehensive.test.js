/**
 * Comprehensive tests for the removeSubtask function
 */
import { jest } from '@jest/globals';

// Mock dependencies using unstable_mockModule for ES modules
jest.unstable_mockModule('../../../../../scripts/modules/utils.js', () => ({
	log: jest.fn(),
	readJSON: jest.fn(),
	writeJSON: jest.fn()
}));

jest.unstable_mockModule('../../../../../scripts/modules/task-manager/generate-task-files.js', () => ({
	default: jest.fn()
}));

jest.unstable_mockModule('path', () => ({
	default: {
		dirname: jest.fn().mockReturnValue('/mock/tasks')
	},
	dirname: jest.fn().mockReturnValue('/mock/tasks')
}));

// Import the mocked modules
const { log, readJSON, writeJSON } = await import('../../../../../scripts/modules/utils.js');
const generateTaskFiles = await import('../../../../../scripts/modules/task-manager/generate-task-files.js');

// Import the function to test
const removeSubtask = (await import('../../../../../scripts/modules/task-manager/remove-subtask.js')).default;

describe('removeSubtask - Comprehensive Tests', () => {
	beforeEach(() => {
		jest.clearAllMocks();
		generateTaskFiles.default.mockResolvedValue();
	});

	const mockTasksData = {
		tasks: [
			{
				id: 1,
				title: 'Parent Task',
				description: 'A parent task',
				status: 'pending',
				dependencies: [],
				subtasks: [
					{
						id: 1,
						title: 'Subtask 1',
						description: 'First subtask',
						status: 'pending',
						dependencies: [],
						parentTaskId: 1
					},
					{
						id: 2,
						title: 'Subtask 2',
						description: 'Second subtask',
						status: 'completed',
						dependencies: [1],
						parentTaskId: 1
					}
				]
			}
		]
	};

	describe('Removing subtask without conversion', () => {
		it('should remove a subtask from its parent', async () => {
			readJSON.mockReturnValue(JSON.parse(JSON.stringify(mockTasksData)));

			const result = await removeSubtask('/mock/tasks.json', '1.1');

			expect(writeJSON).toHaveBeenCalledWith(
				'/mock/tasks.json',
				expect.objectContaining({
					tasks: expect.arrayContaining([
						expect.objectContaining({
							id: 1,
							subtasks: expect.arrayContaining([
								expect.objectContaining({
									id: 2,
									title: 'Subtask 2'
								})
							])
						})
					])
				})
			);
			expect(result).toBeNull();
		});

		it('should remove the subtask from parent task subtasks array', async () => {
			readJSON.mockReturnValue(JSON.parse(JSON.stringify(mockTasksData)));

			await removeSubtask('/mock/tasks.json', '1.1');

			const writtenData = writeJSON.mock.calls[0][1];
			const parentTask = writtenData.tasks.find((t) => t.id === 1);

			// The subtask should be removed from the parent's subtasks array
			expect(parentTask.subtasks).toHaveLength(1);
			expect(parentTask.subtasks.find((st) => st.id === 1)).toBeUndefined();
			expect(parentTask.subtasks.find((st) => st.id === 2)).toBeDefined();
		});
	});

	describe('Converting subtask to standalone task', () => {
		it('should convert a subtask to a standalone task', async () => {
			readJSON.mockReturnValue(JSON.parse(JSON.stringify(mockTasksData)));

			const result = await removeSubtask('/mock/tasks.json', '1.1', true);

			const writtenData = writeJSON.mock.calls[0][1];
			const newTask = writtenData.tasks.find((t) => t.id === 2); // Should get next available ID

			expect(newTask).toEqual(
				expect.objectContaining({
					id: 2,
					title: 'Subtask 1',
					description: 'First subtask',
					status: 'pending',
					dependencies: expect.arrayContaining([1]) // Should include parent as dependency
				})
			);
			expect(newTask.parentTaskId).toBeUndefined();
			expect(result).toEqual(newTask);
		});

		it('should add parent task as dependency when converting to standalone task', async () => {
			const dataWithoutParentDep = {
				tasks: [
					{
						id: 1,
						title: 'Parent Task',
						subtasks: [
							{
								id: 1,
								title: 'Subtask 1',
								dependencies: [2], // Has other dependencies but not parent
								parentTaskId: 1
							}
						]
					}
				]
			};
			readJSON.mockReturnValue(dataWithoutParentDep);

			const result = await removeSubtask('/mock/tasks.json', '1.1', true);

			expect(result.dependencies).toContain(1);
			expect(result.dependencies).toContain(2);
		});

		it('should not duplicate parent dependency if already present', async () => {
			const dataWithParentDep = {
				tasks: [
					{
						id: 1,
						title: 'Parent Task',
						subtasks: [
							{
								id: 1,
								title: 'Subtask 1',
								dependencies: [1], // Already depends on parent
								parentTaskId: 1
							}
						]
					}
				]
			};
			readJSON.mockReturnValue(dataWithParentDep);

			const result = await removeSubtask('/mock/tasks.json', '1.1', true);

			const parentDependencies = result.dependencies.filter((dep) => dep === 1);
			expect(parentDependencies).toHaveLength(1);
		});
	});

	describe('Error handling', () => {
		it('should throw error when tasks file is invalid', async () => {
			readJSON.mockReturnValue(null);

			await expect(removeSubtask('/mock/tasks.json', '1.1')).rejects.toThrow(
				'Invalid or missing tasks file at /mock/tasks.json'
			);
		});

		it('should throw error when subtask ID format is invalid', async () => {
			readJSON.mockReturnValue(mockTasksData);

			await expect(removeSubtask('/mock/tasks.json', 'invalid')).rejects.toThrow(
				'Invalid subtask ID format: invalid. Expected format: "parentId.subtaskId"'
			);
		});

		it('should throw error when parent task is not found', async () => {
			readJSON.mockReturnValue(mockTasksData);

			await expect(removeSubtask('/mock/tasks.json', '999.1')).rejects.toThrow(
				'Parent task with ID 999 not found'
			);
		});

		it('should throw error when subtask is not found', async () => {
			readJSON.mockReturnValue(mockTasksData);

			await expect(removeSubtask('/mock/tasks.json', '1.999')).rejects.toThrow(
				'Subtask 1.999 not found'
			);
		});
	});

	describe('Configuration options', () => {
		it('should skip file generation when generateFiles is false', async () => {
			readJSON.mockReturnValue(JSON.parse(JSON.stringify(mockTasksData)));

			await removeSubtask('/mock/tasks.json', '1.1', false, false);

			expect(generateTaskFiles.default).not.toHaveBeenCalled();
		});

		it('should generate files by default', async () => {
			readJSON.mockReturnValue(JSON.parse(JSON.stringify(mockTasksData)));

			await removeSubtask('/mock/tasks.json', '1.1');

			expect(generateTaskFiles.default).toHaveBeenCalledWith(
				'/mock/tasks.json',
				'/mock/tasks'
			);
		});
	});

	describe('Logging', () => {
		it('should log appropriate messages during execution', async () => {
			readJSON.mockReturnValue(JSON.parse(JSON.stringify(mockTasksData)));

			await removeSubtask('/mock/tasks.json', '1.1');

			expect(log).toHaveBeenCalledWith('info', 'Removing subtask 1.1...');
			expect(log).toHaveBeenCalledWith('info', 'Subtask 1.1 deleted');
			expect(log).toHaveBeenCalledWith('info', 'Regenerating task files...');
		});

		it('should log conversion message when converting to task', async () => {
			readJSON.mockReturnValue(JSON.parse(JSON.stringify(mockTasksData)));

			await removeSubtask('/mock/tasks.json', '1.1', true);

			expect(log).toHaveBeenCalledWith(
				'info',
				expect.stringMatching(/Created new task \d+ from subtask 1\.1/)
			);
		});

		it('should log error and rethrow when operation fails', async () => {
			readJSON.mockReturnValue(null);

			await expect(removeSubtask('/mock/tasks.json', '1.1')).rejects.toThrow();

			expect(log).toHaveBeenCalledWith(
				'error',
				expect.stringContaining('Error removing subtask:')
			);
		});
	});
});
